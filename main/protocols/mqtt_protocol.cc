#include "mqtt_protocol.h"
#include "board.h"
#include "application.h"
#include "settings.h"
#include "system_info.h"
#include <esp_log.h>
#include <ml307_mqtt.h>
#include <ml307_udp.h>
#include <cstring>
#include <arpa/inet.h>
#include "assets/lang_config.h"

#define TAG "MQTT"

MqttProtocol::MqttProtocol() {
    event_group_handle_ = xEventGroupCreate();
}

MqttProtocol::~MqttProtocol() {
    ESP_LOGI(TAG, "MqttProtocol deinit");
    if (udp_ != nullptr) {
        delete udp_;
    }
    if (mqtt_ != nullptr) {
        delete mqtt_;
    }
    vEventGroupDelete(event_group_handle_);
}

bool MqttProtocol::Start() {
    return StartMqttClient(false);
}

//--------开启MQTT客户端(连接 与 订阅)
bool MqttProtocol::StartMqttClient(bool report_error) {
    if (mqtt_ != nullptr) {
        ESP_LOGW(TAG, "Mqtt client already started");
        delete mqtt_;
    }

    Settings settings("mqtt", true);//F移植 改false为true
    
    endpoint_ = settings.GetString("endpoint");
    client_id_ = settings.GetString("client_id");
    username_ = settings.GetString("username");
    password_ = settings.GetString("password");
    int keepalive_interval = settings.GetInt("keepalive", 90);
    //publish_topic_ = settings.GetString("publish_topic");//F移植 注释
    subscribe_topic_ = settings.GetString("subscribe_topic");//F移植 添加

    //F移植 添加---------------------------------------------
    std::string user_id = SystemInfo::GetMacAddress()    ;  // 或者其他方式获取 userI
    std::string user_id2 = SystemInfo::GetMacAddressNoColon()    ;  // 或者其他方式获取 userI
    std::string user_id3 = SystemInfo::GetMacAddressDecimal()    ;  // 或者其他方式获取 userI

    ESP_LOGI(TAG, "Client **********: %s", user_id.c_str());  // 打印 client_id_
    ESP_LOGI(TAG, "Client **********: %s", user_id2.c_str());  // 打印 client_id_
    ESP_LOGI(TAG, "Client **********: %s", user_id3.c_str());  // 打印 client_id_

    std::string phone_control_topic = "doll/control/" + user_id3;
    std::string languagesType_topic = "doll/set/" + user_id3;
    std::string moan_topic = "doll/control_moan/" + user_id3;
    user_id3_ =  user_id3;
    
    
    // // 加载 NVS 中保存的语言类型
    std::string saved_language = LoadLanguageTypeFromNVS();
    if (!saved_language.empty()) {
        ESP_LOGI(TAG, "Loaded language type from NVS1233: %s", saved_language.c_str());
        // 在需要的地方使用 saved_language（例如：设置语言）
       // languagesType_ = saved_language;
    }

    publish_topic_ = "stt/doll/" + user_id3 + "/" + saved_language;

    //---------------------------------------------------------



    if (endpoint_.empty()) {
        ESP_LOGW(TAG, "MQTT endpoint is not specified");
        if (report_error) {
            SetError(Lang::Strings::SERVER_NOT_FOUND);
        }
        return false;
    }

    mqtt_ = Board::GetInstance().CreateMqtt();
    mqtt_->SetKeepAlive(keepalive_interval);

    mqtt_->OnDisconnected([this]() {
        ESP_LOGI(TAG, "Disconnected from endpoint");
    });

    //设置处理不同json消息的回调函数
    mqtt_->OnMessage([this,languagesType_topic, phone_control_topic,moan_topic](const std::string& topic, const std::string& payload) {
       
       #if 1 //F移植 添加
        // 只在 Debug 级别打印详细日志
        ESP_LOGD(TAG, "MQTT Message: topic=%s, size=%d", topic.c_str(), payload.size());
        
        // 处理不同的主题
        if (topic == subscribe_topic_) {
            // 处理主语音通信主题
            // 判断是否是 JSON 消息
            if (payload[0] == '{') {
                ESP_LOGI(TAG, "Received JSON message: %s", payload.c_str());
                // JSON 消息交给 OnIncomingJson 处理
                if (on_incoming_json_ != nullptr) {
                    cJSON* root = cJSON_Parse(payload.c_str());
                    if (root != nullptr) {
                        on_incoming_json_(root);
                        cJSON_Delete(root);
                    }
                }
            } else {
                // PCM 数据交给 OnIncomingAudio 处理
                if (on_incoming_audio_ != nullptr) {


                    #if 1  //C3处理版本  使用音频结构体

                        AudioStreamPacket packet;
                        packet.sample_rate = server_sample_rate_;
                        packet.frame_duration = server_frame_duration_;
                        packet.timestamp = 0; // 如有时间戳可补充 否则为0
                        packet.payload.assign(payload.begin(), payload.end());
                        on_incoming_audio_(std::move(packet));

                    #else  //S3处理版本
                        std::vector<uint8_t> audio_data(payload.begin(), payload.end());
                        on_incoming_audio_(std::move(audio_data));

                    #endif
                }
            }
        } 
        else if (topic == phone_control_topic) {
            // 处理控制消息主题
            ESP_LOGI(TAG, "Received control message: %s", payload.c_str());
            
            // 解析控制消息JSON
            cJSON* root = cJSON_Parse(payload.c_str());
            if (root != nullptr) {
                // 直接传给应用程序处理
                if (on_incoming_json_ != nullptr) {
                    on_incoming_json_(root);
                }
                cJSON_Delete(root);
            } else {
                ESP_LOGE(TAG, "Failed to parse control message JSON");
            }
        }
        else if (topic == languagesType_topic) {
            // 处理语言设置消息
            ESP_LOGI(TAG, "Received language setting: %s", payload.c_str());
            // 处理语言设置逻辑...
            cJSON* root = cJSON_Parse(payload.c_str());
            if (root != nullptr) {
                if (on_incoming_json_ != nullptr) {
                    on_incoming_json_(root);
                }
                cJSON_Delete(root);
            }
        } else if (topic == moan_topic) {
          // 处理语言设置消息
          ESP_LOGI(TAG, "Received moan: %s", payload.c_str());
          // 处理语言设置逻辑...
          cJSON* root = cJSON_Parse(payload.c_str());
          if (root != nullptr) {
            if (on_incoming_json_ != nullptr) {
              on_incoming_json_(root);
            }
            cJSON_Delete(root);
          }
        } else {
          ESP_LOGW(TAG, "Unhandled topic: %s", topic.c_str());
        }



       #else

        cJSON* root = cJSON_Parse(payload.c_str());
        if (root == nullptr) {
            ESP_LOGE(TAG, "Failed to parse json message %s", payload.c_str());
            return;
        }
        cJSON* type = cJSON_GetObjectItem(root, "type");
        if (!cJSON_IsString(type)) {
            ESP_LOGE(TAG, "Message type is invalid");
            cJSON_Delete(root);
            return;
        }

        if (strcmp(type->valuestring, "hello") == 0) {
            ParseServerHello(root);
        } else if (strcmp(type->valuestring, "goodbye") == 0) {
            auto session_id = cJSON_GetObjectItem(root, "session_id");
            ESP_LOGI(TAG, "Received goodbye message, session_id: %s", session_id ? session_id->valuestring : "null");
            if (session_id == nullptr || session_id_ == session_id->valuestring) {
                Application::GetInstance().Schedule([this]() {
                    CloseAudioChannel();
                });
            }
        } else if (on_incoming_json_ != nullptr) {
            on_incoming_json_(root);
        }
        cJSON_Delete(root);
        last_incoming_time_ = std::chrono::steady_clock::now();
        #endif
    });

    // ESP_LOGI(TAG, "Connecting to endpoint %s", endpoint.c_str());
    // std::string broker_address;
    // int broker_port = 8883;
    // size_t pos = endpoint.find(':');
    // if (pos != std::string::npos) {
    //     broker_address = endpoint.substr(0, pos);
    //     broker_port = std::stoi(endpoint.substr(pos + 1));
    // } else {
    //     broker_address = endpoint;
    // }


        // 在连接前添加此日志检查配置
    ESP_LOGI(TAG, "MQTT配置: endpoint='%s', client_id='%s', username='%s'",
             endpoint_.c_str(), client_id_.c_str(), username_.c_str());

    // 尝试连接 如果失败做相应处理
    ESP_LOGI(TAG, "Connecting to MQTT broker: %s", endpoint_.c_str());
    if (!mqtt_->Connect(endpoint_, MWTT_PORT, client_id_, username_, password_)) {
        ESP_LOGE(TAG, "Failed to connect to endpoint");
        SetError(Lang::Strings::SERVER_NOT_CONNECTED);
        return false;
    }

    ESP_LOGI(TAG, "Connected to endpoint");

    //F移植 添加
    //mqtt连接成功后相应处理
    if (!subscribe_topic_.empty()) {
    mqtt_->Subscribe(subscribe_topic_, 2); // 2为QoS，可根据需要调整
    ESP_LOGI(TAG, "Subscribing to topic: %s", subscribe_topic_.c_str());       
    mqtt_->Subscribe(phone_control_topic, 0); 
    ESP_LOGI(TAG, "phone_control_topic: %s", phone_control_topic.c_str());
    mqtt_->Subscribe(languagesType_topic, 0); 
    ESP_LOGI(TAG, "languagesType_topic: %s", languagesType_topic.c_str());
    mqtt_->Subscribe(moan_topic, 0); 
    ESP_LOGI(TAG, "moan_topic: %s", languagesType_topic.c_str());

    }

    return true;
}


// Add new method to update language
void MqttProtocol::UpdateLanguage(const std::string& language) {
    languagesType_ = language;
    
    // Update the publish topic with the new language
    std::string user_id = SystemInfo::GetMacAddressDecimal();
    publish_topic_ = "stt/doll/" + user_id + "/" + language;
    
    ESP_LOGI(TAG, "Updated publish topic to: %s, language: %s", publish_topic_.c_str(), language.c_str());
}

void MqttProtocol::WakeupCall() {
    std::string user_id = SystemInfo::GetMacAddressDecimal();
    std::string wakeup_topic = "stt/audio/text";
    
    // Create JSON message according to protocol specification
    cJSON* root = cJSON_CreateObject();
    cJSON_AddStringToObject(root, "device_id", user_id.c_str());
    cJSON_AddStringToObject(root, "device_type", "doll"); 
    cJSON_AddStringToObject(root, "stt_text", "Device is ready#");
    cJSON_AddStringToObject(root, "modal_type", "audio");
    char* json_string = cJSON_PrintUnformatted(root);
    mqtt_->Publish(wakeup_topic, json_string);
    
    // Free allocated memory
    free(json_string);
    cJSON_Delete(root);
    
    ESP_LOGI(TAG, "Published wakeup call to %s", wakeup_topic.c_str());
}


std::string MqttProtocol::LoadLanguageTypeFromNVS() {
    std::string saved_language;
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("config", NVS_READWRITE, &nvs_handle);
    if (err == ESP_OK) {
        size_t required_size;
        err = nvs_get_str(nvs_handle, "languagesType", NULL, &required_size);
        if (err == ESP_OK) {
            char* lang = new char[required_size];
            err = nvs_get_str(nvs_handle, "languagesType", lang, &required_size);
            if (err == ESP_OK) {
                saved_language = std::string(lang);
            }
            delete[] lang;
        }
        nvs_close(nvs_handle);
    }
    return saved_language;
}


bool MqttProtocol::SendText(const std::string& text) {
    if (publish_topic_.empty()) {
        return false;
    }
    if (!mqtt_->Publish(publish_topic_, text)) {
        ESP_LOGE(TAG, "Failed to publish message: %s", text.c_str());
        SetError(Lang::Strings::SERVER_ERROR);
        return false;
    }
    return true;
}

bool MqttProtocol::SendAudio(const AudioStreamPacket& packet) {
    std::lock_guard<std::mutex> lock(channel_mutex_);
    if (udp_ == nullptr) {
        return false;
    }

    std::string nonce(aes_nonce_);
    *(uint16_t*)&nonce[2] = htons(packet.payload.size());
    *(uint32_t*)&nonce[8] = htonl(packet.timestamp);
    *(uint32_t*)&nonce[12] = htonl(++local_sequence_);

    std::string encrypted;
    encrypted.resize(aes_nonce_.size() + packet.payload.size());
    memcpy(encrypted.data(), nonce.data(), nonce.size());

    size_t nc_off = 0;
    uint8_t stream_block[16] = {0};
    if (mbedtls_aes_crypt_ctr(&aes_ctx_, packet.payload.size(), &nc_off, (uint8_t*)nonce.c_str(), stream_block,
        (uint8_t*)packet.payload.data(), (uint8_t*)&encrypted[nonce.size()]) != 0) {
        ESP_LOGE(TAG, "Failed to encrypt audio data");
        return false;
    }

    return udp_->Send(encrypted) > 0;
}


void MqttProtocol::SendImuStatesAndValue(const t_sQMI8658& imu_data,
                                         int touch_value) {
  if (mqtt_ == nullptr || !mqtt_->IsConnected()) {
    ESP_LOGE(TAG, "MQTT client not connected");
    return;
  }

  if (user_id3_.empty()) {
    ESP_LOGE(TAG, "User ID is empty");
    return;
  }

  // 构建JSON消息
  cJSON* root = cJSON_CreateObject();
  if (root == NULL) {
    ESP_LOGE(TAG, "Failed to create JSON object");
    return;
  }

  // 添加state字段
  int touch_value_temp = touch_value;
 
  cJSON_AddNumberToObject(root, "imu_type", imu_data.motion);
  cJSON_AddNumberToObject(root, "gx", imu_data.gyr_x);
  cJSON_AddNumberToObject(root, "gy", imu_data.gyr_y);
  cJSON_AddNumberToObject(root, "gz", imu_data.gyr_z);
  cJSON_AddNumberToObject(root, "ax", imu_data.acc_x);
  cJSON_AddNumberToObject(root, "ay", imu_data.acc_y);
  cJSON_AddNumberToObject(root, "az", imu_data.acc_z);
  cJSON_AddNumberToObject(root, "touch_value", touch_value_temp);
  // 添加device_id字段
  //
 
  cJSON_AddStringToObject(root, "device_id", user_id3_.c_str());

  // 将JSON转换为字符串
  char* message_str = cJSON_PrintUnformatted(root);
  if (message_str == NULL) {
    ESP_LOGE(TAG, "Failed to print JSON");
    cJSON_Delete(root);
    return;
  }

  std::string message(message_str);
  std::string imu_topic = "doll/imu_status";

  ESP_LOGI(TAG, "Sending IMU data: %s to topic: %s", message_str,
           imu_topic.c_str());

  // 发布消息
  mqtt_->Publish(imu_topic, message);

  // 清理资源
  cJSON_free(message_str);
  cJSON_Delete(root);
}



void MqttProtocol::CloseAudioChannel() {
    {
        std::lock_guard<std::mutex> lock(channel_mutex_);
        if (udp_ != nullptr) {
            delete udp_;
            udp_ = nullptr;
        }
    }

    ESP_LOGI(TAG, "Sending end of audio stream");
    mqtt_->Publish(publish_topic_, "END", 1);

    if (on_audio_channel_closed_ != nullptr) {
        on_audio_channel_closed_();
    }
}

bool MqttProtocol::OpenAudioChannel() {
    if (mqtt_ == nullptr || !mqtt_->IsConnected()) {
        ESP_LOGI(TAG, "MQTT is not connected, try to connect now");
        if (!StartMqttClient(true)) {
            return false;
        }
    }

    error_occurred_ = false;
    session_id_ = "";
    xEventGroupClearBits(event_group_handle_, MQTT_PROTOCOL_SERVER_HELLO_EVENT);

    auto message = GetHelloMessage();
    if (!SendText(message)) {
        return false;
    }

    // 等待服务器响应
    EventBits_t bits = xEventGroupWaitBits(event_group_handle_, MQTT_PROTOCOL_SERVER_HELLO_EVENT, pdTRUE, pdFALSE, pdMS_TO_TICKS(10000));
    if (!(bits & MQTT_PROTOCOL_SERVER_HELLO_EVENT)) {
        ESP_LOGE(TAG, "Failed to receive server hello");
        SetError(Lang::Strings::SERVER_TIMEOUT);
        return false;
    }

    std::lock_guard<std::mutex> lock(channel_mutex_);
    if (udp_ != nullptr) {
        delete udp_;
    }
    udp_ = Board::GetInstance().CreateUdp();
    udp_->OnMessage([this](const std::string& data) {
        /*
         * UDP Encrypted OPUS Packet Format:
         * |type 1u|flags 1u|payload_len 2u|ssrc 4u|timestamp 4u|sequence 4u|
         * |payload payload_len|
         */
        if (data.size() < sizeof(aes_nonce_)) {
            ESP_LOGE(TAG, "Invalid audio packet size: %u", data.size());
            return;
        }
        if (data[0] != 0x01) {
            ESP_LOGE(TAG, "Invalid audio packet type: %x", data[0]);
            return;
        }
        uint32_t timestamp = ntohl(*(uint32_t*)&data[8]);
        uint32_t sequence = ntohl(*(uint32_t*)&data[12]);
        if (sequence < remote_sequence_) {
            ESP_LOGW(TAG, "Received audio packet with old sequence: %lu, expected: %lu", sequence, remote_sequence_);
            return;
        }
        if (sequence != remote_sequence_ + 1) {
            ESP_LOGW(TAG, "Received audio packet with wrong sequence: %lu, expected: %lu", sequence, remote_sequence_ + 1);
        }

        size_t decrypted_size = data.size() - aes_nonce_.size();
        size_t nc_off = 0;
        uint8_t stream_block[16] = {0};
        auto nonce = (uint8_t*)data.data();
        auto encrypted = (uint8_t*)data.data() + aes_nonce_.size();
        AudioStreamPacket packet;
        packet.sample_rate = server_sample_rate_;
        packet.frame_duration = server_frame_duration_;
        packet.timestamp = timestamp;
        packet.payload.resize(decrypted_size);
        int ret = mbedtls_aes_crypt_ctr(&aes_ctx_, decrypted_size, &nc_off, nonce, stream_block, encrypted, (uint8_t*)packet.payload.data());
        if (ret != 0) {
            ESP_LOGE(TAG, "Failed to decrypt audio data, ret: %d", ret);
            return;
        }
        if (on_incoming_audio_ != nullptr) {
            on_incoming_audio_(std::move(packet));
        }
        remote_sequence_ = sequence;
        last_incoming_time_ = std::chrono::steady_clock::now();
    });

    udp_->Connect(udp_server_, udp_port_);

    if (on_audio_channel_opened_ != nullptr) {
        on_audio_channel_opened_();
    }
    return true;
}

std::string MqttProtocol::GetHelloMessage() {
    // 发送 hello 消息申请 UDP 通道
    cJSON* root = cJSON_CreateObject();
    cJSON_AddStringToObject(root, "type", "hello");
    cJSON_AddNumberToObject(root, "version", 3);
    cJSON_AddStringToObject(root, "transport", "udp");
    cJSON* features = cJSON_CreateObject();
#if CONFIG_USE_SERVER_AEC
    cJSON_AddBoolToObject(features, "aec", true);
#endif
#if CONFIG_IOT_PROTOCOL_MCP
    cJSON_AddBoolToObject(features, "mcp", true);
#endif
    cJSON_AddItemToObject(root, "features", features);
    cJSON* audio_params = cJSON_CreateObject();
    cJSON_AddStringToObject(audio_params, "format", "opus");
    cJSON_AddNumberToObject(audio_params, "sample_rate", 16000);
    cJSON_AddNumberToObject(audio_params, "channels", 1);
    cJSON_AddNumberToObject(audio_params, "frame_duration", OPUS_FRAME_DURATION_MS);
    cJSON_AddItemToObject(root, "audio_params", audio_params);
    auto json_str = cJSON_PrintUnformatted(root);
    std::string message(json_str);
    cJSON_free(json_str);
    cJSON_Delete(root);
    return message;
}

void MqttProtocol::ParseServerHello(const cJSON* root) {
    auto transport = cJSON_GetObjectItem(root, "transport");
    if (transport == nullptr || strcmp(transport->valuestring, "udp") != 0) {
        ESP_LOGE(TAG, "Unsupported transport: %s", transport->valuestring);
        return;
    }

    auto session_id = cJSON_GetObjectItem(root, "session_id");
    if (cJSON_IsString(session_id)) {
        session_id_ = session_id->valuestring;
        ESP_LOGI(TAG, "Session ID: %s", session_id_.c_str());
    }

    // Get sample rate from hello message
    auto audio_params = cJSON_GetObjectItem(root, "audio_params");
    if (cJSON_IsObject(audio_params)) {
        auto sample_rate = cJSON_GetObjectItem(audio_params, "sample_rate");
        if (cJSON_IsNumber(sample_rate)) {
            server_sample_rate_ = sample_rate->valueint;
        }
        auto frame_duration = cJSON_GetObjectItem(audio_params, "frame_duration");
        if (cJSON_IsNumber(frame_duration)) {
            server_frame_duration_ = frame_duration->valueint;
        }
    }

    auto udp = cJSON_GetObjectItem(root, "udp");
    if (!cJSON_IsObject(udp)) {
        ESP_LOGE(TAG, "UDP is not specified");
        return;
    }
    udp_server_ = cJSON_GetObjectItem(udp, "server")->valuestring;
    udp_port_ = cJSON_GetObjectItem(udp, "port")->valueint;
    auto key = cJSON_GetObjectItem(udp, "key")->valuestring;
    auto nonce = cJSON_GetObjectItem(udp, "nonce")->valuestring;

    // auto encryption = cJSON_GetObjectItem(udp, "encryption")->valuestring;
    // ESP_LOGI(TAG, "UDP server: %s, port: %d, encryption: %s", udp_server_.c_str(), udp_port_, encryption);
    aes_nonce_ = DecodeHexString(nonce);
    mbedtls_aes_init(&aes_ctx_);
    mbedtls_aes_setkey_enc(&aes_ctx_, (const unsigned char*)DecodeHexString(key).c_str(), 128);
    local_sequence_ = 0;
    remote_sequence_ = 0;
    xEventGroupSetBits(event_group_handle_, MQTT_PROTOCOL_SERVER_HELLO_EVENT);
}

static const char hex_chars[] = "0123456789ABCDEF";
// 辅助函数，将单个十六进制字符转换为对应的数值
static inline uint8_t CharToHex(char c) {
    if (c >= '0' && c <= '9') return c - '0';
    if (c >= 'A' && c <= 'F') return c - 'A' + 10;
    if (c >= 'a' && c <= 'f') return c - 'a' + 10;
    return 0;  // 对于无效输入，返回0
}

std::string MqttProtocol::DecodeHexString(const std::string& hex_string) {
    std::string decoded;
    decoded.reserve(hex_string.size() / 2);
    for (size_t i = 0; i < hex_string.size(); i += 2) {
        char byte = (CharToHex(hex_string[i]) << 4) | CharToHex(hex_string[i + 1]);
        decoded.push_back(byte);
    }
    return decoded;
}

bool MqttProtocol::IsAudioChannelOpened() const {
    return udp_ != nullptr && !error_occurred_ && !IsTimeout();
}



void MqttProtocol::SendCancelTTS(bool f) {
    // 获取设备ID（十进制MAC地址）
    std::string device_id = SystemInfo::GetMacAddressDecimal();
    // 构造JSON消息
    std::string message;
    if (f) {
      message = "{\"user_id\":\"" + device_id + "\",\"action\":\"finish\"}";
    } else {
      message = "{\"user_id\":\"" + device_id + "\",\"action\":\"stop\"}";
    }

    // 打印日志
    ESP_LOGI(TAG, "Sending CancelTTS message: %s", message.c_str());
    
    // 发送到tts/cancel主题
    mqtt_->Publish("tts/cancel", message, 2);
    
    ESP_LOGI(TAG, "CancelTTS message sent to topic: tts/cancel");
}